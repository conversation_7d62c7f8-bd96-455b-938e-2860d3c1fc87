<template>
  <div class="home-container">
    <!-- 统一吸顶容器 -->
    <div class="sticky-header" :class="{
      'sticky-header-scrolled': isScrolled,
      'sticky-header-no-banner': !hasBannerData
    }" :style="getStickyHeaderStyle()">
      <!-- 搜索栏 -->
      <div class="search-input" @click="goToSearch">
        <van-icon name="search" />
        <span class="placeholder">请输入活动名称或编号进行搜索</span>
      </div>

      <!-- 活动类型区域 -->
      <div class="category-container" v-if="isScrolled">
        <div class="category-scroll" :class="{ 'can-scroll': allCategories.length > 4 }">
          <!-- 全部活动 - 固定第一位 -->
          <div
            class="category-item"
            :class="{ active: activeCategory === 'all' }"
            @click="handleCategoryChange('all')"
          >
            <div class="category-icon-wrapper">
              <div class="category-icon all-icon">
                <div class="all-icon-grid">
                  <div class="grid-dot"></div>
                  <div class="grid-dot"></div>
                  <div class="grid-dot"></div>
                  <div class="grid-dot"></div>
                </div>
              </div>
            </div>
            <span class="category-name">全部活动</span>
          </div>

          <!-- 其他活动类型 - 按排序升序展示 -->
          <div
            v-for="category in sortedCategories"
            :key="category.id"
            class="category-item"
            :class="{ active: activeCategory === category.id }"
            @click="handleCategoryChange(category.id)"
          >
            <div class="category-icon-wrapper">
              <div class="category-icon">
                <img :src="category.icon" :alt="category.name" />
              </div>
            </div>
            <span class="category-name">{{ category.name }}</span>
          </div>
        </div>
      </div>

      <!-- 筛选区域 -->
      <div class="filter-section" v-if="isScrolled">
        <div class="sort-buttons">
          <button
            class="sort-btn"
            :class="{ active: sortType === 'time' }"
            @click="handleSort('time')"
          >
            最近发布
          </button>
          <button
            class="sort-btn popularity-btn"
            :class="{ active: sortType === 'popularity' }"
            @click="handleSort('popularity')"
          >
            人气排行
            <van-icon :name="popularityOrder === 'asc' ? 'arrow-up' : 'arrow-down'" v-if="sortType === 'popularity'" />
          </button>
        </div>
        <div style="display: flex; gap: 8px;">
          <button
            class="filter-btn"
            :class="{ active: hasActiveFilter || showFilterPopup }"
            @click="toggleFilter"
          >
            筛选
          </button>
        </div>
      </div>
    </div>

    <!-- 页面内容 -->
    <div class="content" @scroll="handleScroll" ref="content">
      <!-- Banner轮播 -->
      <div class="banner-section" v-if="bannerList && bannerList.length > 0">
        <template v-if="bannerList.length > 1">
          <div class="swipe-wrapper" v-if="bannerSwipeReady">
            <van-swipe
              ref="bannerSwipe"
              class="banner-swipe"
              :key="`banner-${bannerList.length}-${bannerSwipeReady}-${Date.now()}`"
              :autoplay="3000"
              :loop="true"
              indicator-color="white"
              :show-indicators="bannerList.length > 1"
              :touchable="bannerList.length > 1"
              :lazy-render="false"
              :swipe-threshold="0"
              @change="onBannerChange"
            >
              <van-swipe-item v-for="(banner, index) in bannerList" :key="`banner-item-${index}`" @click="handleBannerClick(banner)">
                <img
                  :src="banner.imageUrl"
                  :alt="banner.title"
                  class="banner-image"
                  style="width: 100%; height: 100%; object-fit: cover;"
                  @load="onBannerImageLoad"
                  @error="onBannerImageError"
                />
              </van-swipe-item>
            </van-swipe>
          </div>
          <!-- 加载占位符 -->
          <div v-else class="banner-placeholder">
            <div class="banner-loading">轮播图加载中...</div>
          </div>
        </template>
        <template v-else>
          <div class="banner-single">
            <img
              :src="bannerList[0].imageUrl"
              :alt="bannerList[0].title"
              class="banner-image"
              @click="handleBannerClick(bannerList[0])"
              style="width: 100%; height: 100%; object-fit: cover;"
              @load="onBannerImageLoad"
              @error="onBannerImageError"
            />
          </div>
        </template>
      </div>

      <!-- 精选活动 -->
      <div class="featured-section" v-if="featuredList.length > 0">
        <h3 class="section-title">精选活动</h3>
        <div class="featured-grid">
          <div 
            v-for="(item, index) in featuredList.slice(0, 4)" 
            :key="index"
            class="featured-item"
            :class="`featured-item-${index + 1}`"
            @click="handleFeaturedClick(item)"
          >
            <div class="featured-icon">
              <img :src="item.imageUrl" :alt="item.title" class="featured-image" />
            </div>
            <div class="featured-content">
              <h4 class="featured-title">{{ item.title }}</h4>
              <p class="featured-subtitle">{{ item.subtitle }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 活动类型 -->
      <div v-if="categoryList && categoryList.length > 0">
        <div
          ref="categorySection"
          class="category-section"
        >
          <div class="category-container">
            <div class="category-scroll" :class="{ 'can-scroll': allCategories.length > 4 }">
              <!-- 全部活动 - 固定第一位 -->
              <div
                class="category-item"
                :class="{ active: activeCategory === 'all' }"
                @click="handleCategoryChange('all')"
              >
                <div class="category-icon-wrapper">
                  <div class="category-icon all-icon">
                    <div class="all-icon-grid">
                      <div class="grid-dot"></div>
                      <div class="grid-dot"></div>
                      <div class="grid-dot"></div>
                      <div class="grid-dot"></div>
                    </div>
                  </div>
                </div>
                <span class="category-name">全部活动</span>
              </div>

              <!-- 其他活动类型 - 按排序升序展示 -->
              <div
                v-for="category in sortedCategories"
                :key="category.id"
                class="category-item"
                :class="{ active: activeCategory === category.id }"
                @click="handleCategoryChange(category.id)"
              >
                <div class="category-icon-wrapper">
                  <div class="category-icon">
                    <img :src="category.icon" :alt="category.name" />
                  </div>
                </div>
                <span class="category-name">{{ category.name }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 排序和筛选 -->
      <div
        class="filter-section"

        v-if="!isScrolled"
      >
        <div class="sort-buttons">
          <button
            class="sort-btn"
            :class="{ active: sortType === 'time' }"
            @click="handleSort('time')"
          >
            最近发布
          </button>
          <button
            class="sort-btn popularity-btn"
            :class="{ active: sortType === 'popularity' }"
            @click="handleSort('popularity')"
          >
            人气排行
            <van-icon :name="popularityOrder === 'asc' ? 'arrow-up' : 'arrow-down'" v-if="sortType === 'popularity'" />
          </button>
        </div>
        <div style="display: flex; gap: 8px;">
          <button
            class="filter-btn"
            :class="{ active: hasActiveFilter || showFilterPopup }"
            @click="toggleFilter"
          >
            筛选
          </button>
        </div>
      </div>

      <!-- 活动列表 + 下拉刷新 -->
      <div class="activity-list">
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
            ref="activityListRef"
            v-model="loading"
            :finished="finished"
            :immediate-check="false"
            finished-text="—我也是有底线的—"
            loading-text="加载中..."
            @load="loadActivityList"
            v-if="categoryList && categoryList.length > 0"
          >
          <div
            v-for="activity in activityList"
            :key="activity.id"
            class="activity-item"
            @click="goToActivityDetail(activity)"
          >
            <!-- 活动标题 -->
            <div class="activity-title-wrapper">
              <h4 class="activity-title">{{ activity.title }}</h4>
              <div class="activity-code">活动编号：{{ activity.id }}</div>
            </div>

            <!-- 活动头图 -->
            <img :src="activity.imageUrl" :alt="activity.title" class="activity-image" />

            <!-- 活动信息 -->
            <div class="activity-info-section">
              <div class="info-row">
                <span class="info-label">活动状态：</span>
                <span class="activity-status" :class="activity.statusClass">{{ activity.statusText }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">报名时间：</span>
                <span class="info-value">yyyy/mm/dd HH:mm - yyyy/mm/dd HH:mm</span>
              </div>
              <div class="info-row">
                <span class="info-label">活动时间：</span>
                <span class="info-value">yyyy/mm/dd HH:mm - yyyy/mm/dd HH:mm</span>
              </div>
              <div class="info-row">
                <span class="info-label">报名人数：</span>
                <span class="info-value">{{ activity.participantCount }}/{{ getMaxParticipantsText(activity.maxParticipants) }} 人</span>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="activity-actions" v-if="shouldShowButton(activity)">
              <button
                class="action-button"
                :class="getButtonClass(activity)"
                @click.stop="handleActivityAction(activity)"
              >
                {{ getButtonText(activity) }}
              </button>
            </div>
          </div>
        </van-list>
        <div v-if="!loading && activityList.length === 0" style="text-align:center;color:#999;padding:16px 0;">
          暂无数据
        </div>
        </van-pull-refresh>
      </div>
    </div>

    <!-- 筛选弹窗 -->
    <van-popup
      v-model="showFilterPopup"
      position="top"
      :style="{
        height: '50vh',
        marginTop: isScrolled ? '236px' : '68px',
        borderRadius: '0'
      }"
      :overlay-style="{ background: 'rgba(0, 0, 0, 0.3)' }"
      @click-overlay="showFilterPopup = false"
    >
      <div class="filter-popup">
        <div class="filter-content">
          <!-- 活动类型筛选 -->
          <div class="filter-group">
            <h4 class="filter-title">活动类型</h4>
            <div class="filter-options">
              <button 
                v-for="category in allCategories" 
                :key="category.id"
                class="filter-option-btn"
                :class="{ active: filterForm.categoryId === category.id }"
                @click="filterForm.categoryId = category.id"
              >
                {{ category.name }}
              </button>
            </div>
          </div>

          <!-- 活动状态筛选 -->
          <div class="filter-group">
            <h4 class="filter-title">活动状态</h4>
            <div class="filter-options">
              <button 
                v-for="status in activityStatusOptions" 
                :key="status.value"
                class="filter-option-btn"
                :class="{ active: filterForm.activityStatus === status.value }"
                @click="filterForm.activityStatus = status.value"
              >
                {{ status.label }}
              </button>
            </div>
          </div>

          <!-- 报名状态筛选 -->
          <div class="filter-group">
            <h4 class="filter-title">活动报名状态</h4>
            <div class="filter-options">
              <button 
                v-for="status in registrationStatusOptions" 
                :key="status.value"
                class="filter-option-btn"
                :class="{ active: filterForm.registrationStatus === status.value }"
                @click="filterForm.registrationStatus = status.value"
              >
                {{ status.label }}
              </button>
            </div>
          </div>
        </div>

        <div class="filter-footer">
          <button class="filter-reset-btn" @click="resetFilter">重置</button>
          <button class="filter-confirm-btn" @click="applyFilter">确定</button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import Vue from 'vue';
import {
  Swipe,
  SwipeItem,
  List,
  Button,
  Popup,
  Icon,
  PullRefresh
} from 'vant';

Vue.use(Swipe)
  .use(SwipeItem)
  .use(List)
  .use(Button)
  .use(Popup)
  .use(Icon)
  .use(PullRefresh);

export default {
  name: 'Home',
  data() {
    return {
      isScrolled: false,
      scrollOpacity: 0, // 滚动透明度
      bannerList: [],
      bannerSwipeReady: false, // banner轮播组件是否准备就绪
      bannerImagesLoaded: 0, // 已加载的banner图片数量
      featuredList: [],
      categoryList: [],
      activityList: [],
      activeCategory: 'all',
      sortType: 'time', // 'time' | 'popularity'
      popularityOrder: 'desc', // 'asc' | 'desc'
      loading: false,
      finished: false,
      page: 1,
      pageSize: 10,
      total: 0,
      refreshing: false,
      showFilterPopup: false,
      filterForm: {
        categoryId: 'all',
        activityStatus: 'all',
        registrationStatus: 'all'
      },
      activityStatusOptions: [
        { label: '全部', value: 'all' },
        { label: '活动未开始', value: 'not_started' },
        { label: '活动进行中', value: 'ongoing' },
        { label: '活动已结束', value: 'ended' }
      ],
      registrationStatusOptions: [
        { label: '全部', value: 'all' },
        { label: '报名未开始', value: 'not_started' },
        { label: '报名进行中', value: 'ongoing' },
        { label: '报名已结束', value: 'ended' }
      ],

      // 错误处理相关
      errorRetryCount: 0, // 错误重试次数
      maxRetryCount: 3, // 最大重试次数
      lastErrorTime: 0 // 上次错误时间
    };
  },
  computed: {

    // 按排序升序排列的活动类型
    sortedCategories() {
      return [...this.categoryList].sort((a, b) => (a.sort || 0) - (b.sort || 0));
    },

    // 所有活动类型（包括"全部"）
    allCategories() {
      return [{ id: 'all', name: '全部活动' }, ...this.sortedCategories];
    },
    hasActiveFilter() {
      return this.filterForm.categoryId !== 'all' ||
             this.filterForm.activityStatus !== 'all' ||
             this.filterForm.registrationStatus !== 'all';
    },
    hasBannerData() {
      return this.bannerList && this.bannerList.length > 0;
    },
    tabLineStyle() {
      // 计算当前激活 tab 的位置来设置下划线位置
      const allTabs = [{ id: 'all' }, ...this.categoryList];
      const activeIndex = allTabs.findIndex(tab => tab.id === this.activeCategory);

      if (activeIndex === -1) return { display: 'none' };

      // 每个 tab 的宽度大约是容器宽度除以可见 tab 数量
      const tabWidth = 100 / Math.min(allTabs.length, 4); // 最多显示4个tab
      const left = activeIndex * tabWidth;

      return {
        left: `${left}%`,
        width: `${tabWidth}%`,
        transform: 'translateX(0)',
        transition: 'all 0.3s ease'
      };
    }
  },
  watch: {
    bannerList: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          console.log('bannerList数据已加载，准备初始化轮播组件');
          this.bannerSwipeReady = false;
          this.bannerImagesLoaded = 0;

          // 如果只有一张图片，直接标记为准备就绪
          if (newVal.length === 1) {
            this.bannerSwipeReady = true;
          } else {
            // 多张图片时，延迟初始化以确保DOM完全渲染
            this.$nextTick(() => {
              setTimeout(() => {
                // 再次确保DOM更新
                this.$nextTick(() => {
                  this.initSwipeComponent();
                });
              }, 500); // 减少延迟时间到500ms
            });
          }
        }
      },
      immediate: false
    },
    categoryList: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          console.log('categoryList数据已加载');
          // 简单触发列表加载
          this.$nextTick(() => {
            setTimeout(() => {
              if (this.$refs.activityListRef && this.activityList.length === 0) {
                console.log('触发活动列表加载');
                this.$refs.activityListRef.check();
              }
            }, 300);
          });
        }
      },
      immediate: false
    }
  },
  mounted() {
    console.log('Home组件已挂载');
    this.initPage();
    if (this.$refs.content) {
      this.bindScrollEvent();
    }



    // 添加轮播组件错误处理
    this.errorHandler = (event) => {
      if (event.message && (
        event.message.includes('Cannot read properties of null (reading \'width\')') ||
        event.message.includes('Cannot read properties of undefined (reading \'width\')') ||
        event.message.includes('Cannot read properties of null (reading \'offsetWidth\')') ||
        event.message.includes('Cannot read properties of undefined (reading \'offsetWidth\')') ||
        event.message.includes('minOffset') ||
        event.message.includes('move')
      )) {
        console.log('检测到轮播组件错误，尝试重新初始化');
        console.log('错误详情:', event.message, event.filename, event.lineno);
        
        // 检查错误来源，如果是轮播组件相关则处理轮播错误
        if (event.filename && (
          event.filename.includes('index.js') || 
          event.filename.includes('swipe') ||
          event.filename.includes('vant')
        )) {
          this.handleSwipeError();
        }
      }
    };
    window.addEventListener('error', this.errorHandler);
    
    // 添加未捕获的Promise错误处理
    this.unhandledRejectionHandler = (event) => {
      if (event.reason && event.reason.message && (
        event.reason.message.includes('width') ||
        event.reason.message.includes('offsetWidth') ||
        event.reason.message.includes('minOffset')
      )) {
        console.log('检测到Promise轮播组件错误，尝试重新初始化');
        this.handleSwipeError();
      }
    };
    window.addEventListener('unhandledrejection', this.unhandledRejectionHandler);

    // 备用方案：确保活动列表能够加载
    setTimeout(() => {
      if (this.activityList.length === 0 && !this.loading) {
        console.log('备用方案：直接调用loadActivityList');
        this.loadActivityList();
      }
    }, 1000);
  },
  beforeDestroy() {
    // 移除事件监听器
    if (this.$refs.content) {
      this.$refs.content.removeEventListener('scroll', this.handleScroll);
    }
    if (this.errorHandler) {
      window.removeEventListener('error', this.errorHandler);
    }
    if (this.unhandledRejectionHandler) {
      window.removeEventListener('unhandledrejection', this.unhandledRejectionHandler);
    }
  },
  methods: {
    // 安全地初始化轮播组件
    initSwipeComponent() {
      const maxRetries = 5;
      let retryCount = 0;
      
      const checkAndInit = () => {
        retryCount++;
        
        // 检查DOM元素是否存在且有宽度
        const swipeElement = document.querySelector('.banner-swipe');
        const swipeWrapper = document.querySelector('.swipe-wrapper');
        
        if (swipeElement && swipeWrapper && 
            swipeElement.offsetWidth > 0 && 
            swipeWrapper.offsetWidth > 0 &&
            swipeElement.offsetHeight > 0) {
          this.bannerSwipeReady = true;
          console.log('banner轮播组件已准备就绪');
          return;
        }
        
        if (retryCount < maxRetries) {
          console.log(`DOM未准备好，第${retryCount}次重试...`);
          setTimeout(checkAndInit, 200 * retryCount); // 递增延迟
        } else {
          console.log('轮播组件初始化失败，使用备用方案');
          this.bannerSwipeReady = true; // 强制设置为true，避免无限等待
        }
      };
      
      checkAndInit();
    },
    
    // 处理轮播组件错误
    handleSwipeError() {
      const currentTime = Date.now();
      
      // 防抖：如果距离上次错误处理不到5秒，则忽略
      if (currentTime - this.lastErrorTime < 5000) {
        console.log('错误处理防抖：忽略频繁错误');
        return;
      }
      
      // 检查重试次数
      if (this.errorRetryCount >= this.maxRetryCount) {
        console.log('轮播组件错误重试次数已达上限，停止重试');
        return;
      }
      
      this.lastErrorTime = currentTime;
      this.errorRetryCount++;
      
      console.log(`轮播组件出现错误，尝试重新初始化 (第${this.errorRetryCount}次)`);
      this.bannerSwipeReady = false;

      // 清理可能存在的轮播组件引用
      if (this.$refs.bannerSwipe) {
        try {
          // 尝试销毁轮播组件
          this.$refs.bannerSwipe.$destroy && this.$refs.bannerSwipe.$destroy();
        } catch (e) {
          console.log('清理轮播组件时出错:', e);
        }
      }

      // 延迟重新初始化轮播组件
      this.$nextTick(() => {
        setTimeout(() => {
          if (this.bannerList && this.bannerList.length > 1) {
            // 使用新的安全初始化方法
            this.initSwipeComponent();
            // 重置错误计数
            this.errorRetryCount = 0;
          }
        }, 1000 + this.errorRetryCount * 500); // 减少延迟时间
      });
    },
    
    async initPage() {
      console.log('初始化页面');
      try {
        // 并行加载基础数据
        const promises = [
          this.loadBannerList(),
          this.loadFeaturedList(),
          this.loadCategoryList()
        ];

        // 不等待基础数据，直接开始加载活动列表
        this.loadActivityList();

        // 等待基础数据加载完成
        await Promise.all(promises);
        console.log('基础数据加载完成');

      } catch (error) {
        console.error('初始化页面失败:', error);
        // 确保活动列表能够加载
        if (this.activityList.length === 0) {
          this.loadActivityList();
        }
      }
    },
    
    bindScrollEvent() {
      if (this.$refs.content) {
        this.$refs.content.addEventListener('scroll', this.handleScroll);
      }
    },
    
    handleScroll(event) {
      const scrollTop = event.target.scrollTop;
      const bannerHeight = this.hasBannerData ? 237 : 0; // banner高度，如果没有banner则为0

      // 计算需要滚动到活动类型区域才开始吸顶
      // 包括：banner高度 + 精选活动区域高度
      const featuredSectionHeight = this.featuredList.length > 0 ? 120 : 0; // 精选活动区域高度
      const stickyTriggerPoint = bannerHeight + featuredSectionHeight;

      // 计算透明度：从0到1的渐变
      if (scrollTop <= bannerHeight) {
        // 在第一屏范围内，透明度为0
        this.scrollOpacity = 0;
        this.isScrolled = false;
      } else {
        // 超过第一屏，开始渐变
        const fadeDistance = 100; // 渐变距离
        const fadeStart = bannerHeight;
        const fadeEnd = bannerHeight + fadeDistance;

        if (scrollTop <= fadeEnd) {
          // 在渐变范围内
          this.scrollOpacity = Math.min((scrollTop - fadeStart) / fadeDistance, 1);
        } else {
          // 超过渐变范围，完全不透明
          this.scrollOpacity = 1;
        }

        // 滚动到活动类型区域开始时就开始吸顶
        this.isScrolled = scrollTop >= stickyTriggerPoint;
      }
    },
    
    async loadBannerList() {
      try {
        console.log('开始加载Banner列表');
        // 调用API获取banner数据
        const response = await this.$api.getBannerList();
        if (response && response.success === 1) {
          const bannerData = response.value || [];
          console.log('Banner数据加载成功:', bannerData.length, '条');
          this.bannerList = bannerData;
        } else {
          console.log('Banner数据为空或加载失败');
          this.bannerList = [];
        }
      } catch (error) {
        console.error('加载Banner列表失败:', error);
        this.bannerList = [];
      }
    },
    
    async loadFeaturedList() {
      try {
        // 调用API获取精选活动数据
        const response = await this.$api.getFeaturedActivityList();
        if (response && response.success === 1) {
          this.featuredList = response.value || [];
        }
      } catch (error) {
        console.error('加载精选活动失败:', error);
        this.featuredList = [];
      }
    },
    
    async loadCategoryList() {
      try {
        // 调用API获取活动类型数据
        const response = await this.$api.getActivityCategoryList();
        if (response && response.success === 1) {
          this.categoryList = response.value || [];
        }
      } catch (error) {
        console.error('加载活动类型失败:', error);
        this.categoryList = [];
      }
    },
    
    async loadActivityList() {
      if (this.loading) return;

      console.log('开始加载活动列表，page:', this.page);
      this.loading = true;
      try {
        let response;

        // 判断是否使用筛选接口 - 使用计算属性确保逻辑一致
        const hasFilter = this.hasActiveFilter;
        console.log('是否使用筛选接口:', hasFilter, '筛选条件:', this.filterForm);

        // 准备排序参数
        const sortParams = {
          popularity: this.sortType === 'popularity' ? 1 : 0, // 1选择人气排行，0不选
          selectSort: this.popularityOrder === 'asc' ? 1 : 0 // 1升序，0降序
        };
        console.log('排序参数:', sortParams);

        if (hasFilter) {
            // 1.7 首页筛选活动接口
            const params = {
              typeId: this.activeCategory === 'all' ? null : parseInt(this.activeCategory),
              activityStatus: this.filterForm.activityStatus === 'all' ? null : this.filterForm.activityStatus,
              registerStatus: this.filterForm.registrationStatus === 'all' ? null : this.filterForm.registrationStatus,
              page: this.page,
              pageSize: this.pageSize,
              ...sortParams // 传递排序参数
            };
            console.log('调用chooseActivity接口，参数:', params);
            response = await this.$api.chooseActivity(params);
          } else {
            // 1.6 首页的活动列表接口
            const params = {
              typeId: this.activeCategory === 'all' ? null : parseInt(this.activeCategory),
              page: this.page,
              pageSize: this.pageSize,
              ...sortParams // 传递排序参数
            };
            console.log('调用getActivityList接口，参数:', params);
            response = await this.$api.getActivityList(params);
          }
          console.log('API响应:', response);

        if (response && response.success === 1) {
          const activities = response.value || [];
          console.log('获取到的活动数据:', activities.length, '条');

          // 转换数据格式以适配页面显示
          const formattedActivities = activities.map(item => ({
            id: item.id,
            title: item.actTitle,
            imageUrl: item.headerImg || require('@/images/img/background.png'),
            headerImgId: item.headerImgId,
            activityStatus: item.activityStatus,
            statusText: this.getActivityStatusText(item.activityStatus),
            statusClass: this.getStatusClass(item.activityStatus),
            activityTime: item.actTime,
            registrationTime: item.registerTime,
            timeText: item.actTime,
            maxParticipants: item.numRage,
            participantCount: item.registrantsNum,
            userRegistered: false, // 接口未返回，设为默认值
            registrationStatus: 'ongoing' // 默认为进行中
          }));
          console.log('格式化后的活动数据:', formattedActivities.length, '条');

          // 处理数据显示
          if (this.page === 1) {
            this.activityList = formattedActivities;
          } else {
            this.activityList.push(...formattedActivities);
          }

          // 设置加载结束状态（优先依据总数，否则按页大小判断）
          if (typeof response.total === 'number' && response.total >= 0) {
            this.total = response.total;
            this.finished = this.page * this.pageSize >= this.total;
          } else {
            this.finished = formattedActivities.length < this.pageSize;
          }

          // 递增页码
          this.page++;
        } else {
          // 接口返回失败，但success不为1的情况
          console.log('接口返回失败，response:', response);
          this.finished = true;
          if (this.page === 1) {
            this.activityList = [];
          }
        }
      } catch (error) {
        console.error('获取活动列表失败:', error);
        this.finished = true;
        if (this.page === 1) {
          this.activityList = [];
        }
      } finally {
        console.log('加载完成，loading设置为false');
        this.loading = false;
      }
    },
    
    goToSearch() {
      this.$router.push('/search');
    },
    
    getStickyHeaderStyle() {
      // 如果没有banner数据，直接显示渐变背景
      if (!this.hasBannerData) {
        return {
          background: 'linear-gradient(to bottom, #F5EBDE, #F3E5D2)'
        };
      }

      // 如果有banner数据，根据滚动位置动态设置透明度
      if (this.scrollOpacity > 0) {
        return {
          background: `linear-gradient(to bottom,
            rgba(245, 235, 222, ${this.scrollOpacity}),
            rgba(243, 229, 210, ${this.scrollOpacity}))`
        };
      }

      // 默认透明
      return {
        background: 'transparent'
      };
    },
    
    // Banner 相关事件处理
    onBannerChange(index) {
      console.log('Banner切换到:', index);
    },

    onBannerImageLoad() {
      this.bannerImagesLoaded++;
      console.log(`Banner图片加载完成: ${this.bannerImagesLoaded}/${this.bannerList.length}`);
    },

    onBannerImageError(event) {
      console.error('Banner图片加载失败:', event.target.src);
    },

    handleBannerClick(banner) {
      try {
        if (banner.linkType === 'activity') {
          this.goToActivityDetail({ id: banner.linkId });
        } else if (banner.linkType === 'h5') {
          window.open(banner.linkUrl);
        }
      } catch (error) {
        console.error('Banner点击处理失败:', error);
      }
    },
    
    handleFeaturedClick(item) {
      if (item.linkType === 'activity') {
        this.goToActivityDetail({ id: item.linkId });
      } else if (item.linkType === 'h5') {
        window.open(item.linkUrl);
      }
    },
    
    handleCategoryChange(name) {
      console.log('切换分类:', name);
      this.activeCategory = name;
      this.resetList();
    },
    
    handleSort(type) {
      if (type === 'popularity' && this.sortType === 'popularity') {
        // 切换升序/降序
        this.popularityOrder = this.popularityOrder === 'asc' ? 'desc' : 'asc';
      } else {
        this.sortType = type;
        if (type === 'popularity') {
          this.popularityOrder = 'desc';
        }
      }
      this.resetList();
    },
    
    resetFilter() {
      this.filterForm = {
        categoryId: 'all',
        activityStatus: 'all',
        registrationStatus: 'all'
      };
    },

    // 切换筛选弹窗显示/隐藏
    toggleFilter() {
      this.showFilterPopup = !this.showFilterPopup;
    },

    applyFilter() {
      this.showFilterPopup = false;
      // 如果筛选了活动类型，需要同步到tab
      if (this.filterForm.categoryId !== 'all') {
        this.activeCategory = this.filterForm.categoryId;
      }
      this.resetList();
    },
    
    resetList() {
      this.page = 1;
      this.total = 0;
      this.finished = false;
      this.activityList = [];
      this.loadActivityList();
    },
 
    onRefresh() {
      this.refreshing = true;
      this.page = 1;
      this.total = 0;
      this.finished = false;
      this.activityList = [];
      this.loadActivityList().finally(() => {
        this.refreshing = false;
      });
    },
    
    goToActivityDetail(activity) {
      this.$router.push(`/activity/${activity.id}`);
    },

    getActivityStatusText(status) {
      const statusMap = {
        'not_started': '未开始',
        'ongoing': '进行中',
        'ended': '已结束',
        '0': '未开始',
        '1': '进行中',
        '2': '已结束'
      };
      return statusMap[status] || '未知状态';
    },

    getStatusClass(status) {
      const statusMap = {
        'not_started': 'status-not-started',
        'ongoing': 'status-ongoing',
        'ended': 'status-ended',
        '0': 'status-not-started',
        '1': 'status-ongoing',
        '2': 'status-ended'
      };
      return statusMap[status] || 'status-not-started';
    },

    getMaxParticipantsText(maxParticipants) {
      return maxParticipants === 'unlimited' ? '不限' : maxParticipants;
    },

    shouldShowButton(activity) {
      // 确保即使没有registrationStatus也能显示按钮
      if (!activity.registrationStatus) return true;
      // 报名未开始或已结束时隐藏按钮
      return activity.registrationStatus !== 'not_started' &&
             activity.registrationStatus !== 'ended';
    },

    getButtonClass(activity) {
      if (activity.userRegistered) {
        return 'registered';
      }
      return 'primary';
    },

    getButtonText(activity) {
      if (activity.userRegistered) {
        return '已报名';
      }
      return '一键报名';
    },

    handleActivityAction(activity) {
      if (activity.userRegistered) {
        // 已报名，跳转到报名详情页
        this.$router.push(`/registration-detail/${activity.id}`);
      } else {
        // 未报名，跳转到活动详情页进行报名
        this.goToActivityDetail(activity);
      }
    },



    // // 调试方法
    // debugLoadData() {
    //   console.log('=== 调试信息 ===');
    //   console.log('当前状态:', {
    //     loading: this.loading,
    //     finished: this.finished,
    //     page: this.page,
    //     activityListLength: this.activityList.length,
    //     sortType: this.sortType,
    //     activeCategory: this.activeCategory,
    //     hasActiveFilter: this.hasActiveFilter
    //   });

    //   // 重置状态并重新加载
    //   this.page = 1;
    //   this.finished = false;
    //   this.activityList = [];
    //   this.loading = false;

    //   console.log('开始重新加载数据...');
    //   this.loadActivityList();
    // }
  }
};
</script>

<style lang="less" scoped>
.home-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

// 统一吸顶容器
.sticky-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background: transparent;
  transition: all 0.3s ease;
  pointer-events: auto;

  // 没有banner数据时显示渐变背景
  &.sticky-header-no-banner {
    background: linear-gradient(to bottom, #F5EBDE, #F3E5D2);
  }

  // 滚动时的动态背景 - 作为一个整体
  &.sticky-header-scrolled {
    background: linear-gradient(to bottom, #F5EBDE, #F3E5D2);
    backdrop-filter: blur(10px);
  }

  // 搜索框样式
  .search-input {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    margin: 8px 16px 4px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 24px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .van-icon {
      margin-right: 8px;
      color: #666;
      transition: color 0.3s ease;
    }

    .placeholder {
      color: #666;
      font-size: 14px;
      transition: color 0.3s ease;
    }
  }

  // 活动类型区域样式
  .category-container {
    background: linear-gradient(180deg, #F3E5D2 0%, #FFFFFF 100%);
    padding: 8px 16px 6px;

    .category-scroll {
      display: flex;
      gap: 0;

      &:not(.can-scroll) {
        .category-item {
          flex: 1;
        }
      }

      &.can-scroll {
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;

        &::-webkit-scrollbar {
          display: none;
        }

        .category-item {
          min-width: 80px;
          flex-shrink: 0;
        }
      }

      .category-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 8px;
        cursor: pointer;
        transition: all 0.3s ease;

        .category-icon-wrapper {
          margin-bottom: 6px;

          .category-icon {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            border: 1px dashed #D4A574;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;

            img {
              width: 32px;
              height: 32px;
              object-fit: contain;
            }

            // 全部活动的特殊图标
            &.all-icon {
              .all-icon-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                grid-template-rows: 1fr 1fr;
                gap: 4px;
                width: 20px;
                height: 20px;

                .grid-dot {
                  width: 6px;
                  height: 6px;
                  background: #D4A574;
                  border-radius: 50%;
                }
              }
            }
          }
        }

        // 兼容旧版本的直接 .category-icon 样式
        .category-icon {
          width: 48px;
          height: 48px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 6px;
          border: 2px dashed #D4A574;
          background: rgba(212, 165, 116, 0.1);
          transition: all 0.3s ease;

          img {
            width: 32px;
            height: 32px;
            object-fit: cover;
            border-radius: 8px;
          }

          .default-icon {
            font-size: 18px;
            font-weight: bold;
            color: #D4A574;
          }
        }

        .category-name {
          font-size: 12px;
          color: #666;
          text-align: center;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          transition: color 0.3s ease;
        }

        &.active {
          .category-icon-wrapper .category-icon {
            border-color: #D4A574;
            background: rgba(212, 165, 116, 0.1);
            box-shadow: 0 2px 8px rgba(212, 165, 116, 0.2);

            &.all-icon .all-icon-grid .grid-dot {
              background: #D4A574;
            }
          }

          .category-icon {
            border-color: #D4A574;
            background: #D4A574;

            .default-icon {
              color: white;
            }
          }

          .category-name {
            color: #D4A574;
            font-weight: 600;
          }
        }
      }
    }
  }

  // 筛选区域样式
  .filter-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 16px 8px;
    background: white;

    .sort-buttons {
      display: flex;
      gap: 132.5px; // 根据图片调整间距为132.5pt

      .sort-btn {
        background: #F5F5F5;
        border: none;
        color: #666;
        font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; // 苹方-简
        font-size: 11px; // 字号11pt
        font-weight: 400; // 字重Regular
        line-height: 15px; // 行高15pt
        letter-spacing: 0; // 字间距0pt
        padding: 6px 12px; // 减少内边距，让按钮更紧凑
        border-radius: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 2px; // 减少图标和文字的间距
        text-align: left; // 左对齐

        &.active {
          background: linear-gradient(135deg, #9D8262 0%, #432A0C 100%); // 使用筛选弹窗的渐变样式
          border-color: transparent;
          color: white;
          font-weight: 400; // 保持Regular字重
          padding: 4px 8px; // 紧凑的内边距
          justify-content: flex-start; // 左对齐
        }

        &:hover:not(.active) {
          background: #E8E8E8;
        }
      }
    }

    .filter-btn {
      background: #F5F5F5;
      border: none;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 14px;
      color: #666;
      cursor: pointer;
      transition: all 0.3s ease;

      &.active {
        background: #D4A574;
        color: white;
      }

      &:hover:not(.active) {
        background: #E8E8E8;
      }

      &::after {
        content: '▼';
        margin-left: 4px;
        font-size: 10px;
        transition: transform 0.3s ease;
      }

      &.active::after {
        transform: rotate(180deg);
      }
    }
  }
}

.content {
  flex: 1;
  overflow-y: auto;
  padding-top: 0; // 移除顶部padding，让banner图片从顶部开始
}

.banner-section {
  position: relative;

  .swipe-wrapper {
    position: relative;
    overflow: hidden;
  }

  .banner-swipe {
    height: 237px; // 设置为237pt
    width: 375px; // 设置为375pt
    margin: 0 auto; // 居中显示

    .banner-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .banner-single {
    height: 237px; // 设置为237pt
    width: 375px; // 设置为375pt
    margin: 0 auto; // 居中显示

    .banner-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .banner-placeholder {
    height: 237px;
    width: 375px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;

    .banner-loading {
      color: #999;
      font-size: 14px;
    }
  }
}

.featured-section {
  width: 100%; // 占满整行
  margin: 0;
  padding: 0;
  
  h3.section-title {
    position: relative;
    width: 100%;
    height: 35.5px; // 严格控制高度为35.5pt
    margin: 0;
    padding: 0;
    background: linear-gradient(to right, #C08A48 0%, #EEC18B 100%);
    display: flex;
    align-items: center;
    justify-content: flex-start;
    box-sizing: border-box;
    
    // 文字样式
    font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 17px; // 字号17pt
    font-weight: 580; // 字重Semibold (600)
    color: #FFFFFF !important; // 文字颜色#FFFFFF 100%，使用!important确保优先级
    text-align: left; // 左对齐
    line-height: 24px; // 行高24pt
    letter-spacing: 0; // 字间距0pt
    
    // 防止换行
    white-space: nowrap; // 强制不换行
    overflow: hidden; // 隐藏溢出内容
    text-overflow: ellipsis; // 超出部分显示省略号
    
    // 使用padding来定位文字
    padding-left: 23px; // 距离最左侧23pt
    padding-right: 284px; // 距离最右侧284pt
    padding-top: 6px; // 距离最上侧6pt
    padding-bottom: 5.5px; // 距离最下侧5.5pt
  }
  
  .featured-grid {
    display: grid;
    grid-template-columns: 1fr 1fr; // 2列网格
    gap: 9px; // 进一步减小网格间距
    padding: 14px; // 进一步减小内边距
    background: #f8f9fa; // 统一的柔和背景色
    box-sizing: border-box; // 确保padding计算在内
    width: 100%; // 确保宽度不超出
    max-width: 100%; // 限制最大宽度
    
    .featured-item {
      position: relative;
      background: #ffffff; // 基础白色背景
      border-radius: 4px; // 4pt圆角
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: flex-start; // 改为顶部对齐，让图标和文字都从顶部开始
      padding-left: 12px; // 进一步减少内边距
      padding-right: 12px;
      padding-top: 12px; // 添加顶部内边距，给内容一些空间
      padding-bottom: 12px; // 添加底部内边距，给内容一些空间
      min-height: 70px; // 减小最小高度
      box-sizing: border-box; // 确保padding计算在内
      width: 171px; // 确保宽度不超出
      height: 65px; // 确保高度不超出
      max-width: 100%; // 限制最大宽度
      overflow: hidden; // 防止内容溢出
      
      // 多层渐变背景效果
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: 4px;
        pointer-events: none;
        z-index: 1;
        
        // 第一层：蓝色系渐变 (从右到左)
        background: linear-gradient(to left, 
          rgba(231, 243, 255, 0.8) 0%, 
          rgba(255, 255, 255, 0.2) 100%);
      }
      
      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: 4px;
        pointer-events: none;
        z-index: 2;
        
        // 第二层：暖色系对角线渐变
        background: linear-gradient(135deg, 
          rgba(244, 232, 217, 0.6) 0%, 
          rgba(237, 206, 165, 0.4) 70%, 
          rgba(255, 254, 253, 0.1) 100%);
      }
      
      // 白色阴影效果
      box-shadow: 
        -0.5px 0.5px 0px rgba(255, 255, 255, 1),
        0 2px 8px rgba(0, 0, 0, 0.05);
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        background: #f0f2f5;
      }
      
      .featured-icon {
        flex-shrink: 0;
        width: 44px; // 图标宽度44pt
        height: 44px; // 图标高度44pt
        margin-right: 8px; // 保持间距
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 16px; // 小圆弧效果
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        position: relative;
        z-index: 3; // 确保图标在渐变层之上
        
        .featured-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 16px; // 相应减小圆角
        }
      }
      
      .featured-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: flex-start; // 改为顶部对齐
        align-items: flex-start; // 左对齐
        min-width: 0; // 允许内容收缩
        overflow: hidden; // 防止内容溢出
        padding-top: 0; // 移除顶部内边距，让标题与图标顶部对齐
        position: relative;
        z-index: 3; // 确保内容在渐变层之上
        
        .featured-title {
          font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          font-size: 14px; // 字号14pt
          font-weight: 500; // 字重Medium (中黑体)
          margin-bottom: 2px; // 保持间距
          color: #2F2F2F; // 颜色#2F2F2F 100%
          line-height: 20px; // 行高20pt
          letter-spacing: 0; // 字间距0pt
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          text-align: left; // 左对齐
          padding-top: 0; // 确保顶部无内边距
          margin-top: 0; // 确保顶部无外边距
        }
        
        .featured-subtitle {
          font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          font-size: 9px; // 字号9pt
          font-weight: 400; // 字重Regular (常规体)
          color: #966233; // 颜色#966233 100%
          line-height: 12.5px; // 行高12.5pt
          letter-spacing: 0; // 字间距0pt
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          text-align: left; // 左对齐
        }
      }
    }
    
    // 当只有1个时，只显示左上角
    &:has(.featured-item:nth-child(1):last-child) {
      grid-template-columns: 1fr;
      grid-template-rows: auto;
      justify-items: start;
      
      .featured-item {
        max-width: calc(50% - 6px);
      }
    }
    
    // 当只有2个时，只展示一行
    &:has(.featured-item:nth-child(2):last-child) {
      grid-template-columns: 1fr 1fr;
      grid-template-rows: auto;
    }
    
    // 当只有3个时，显示左上、右上、左下，右下空缺
    &:has(.featured-item:nth-child(3):last-child) {
      grid-template-columns: 1fr 1fr;
      grid-template-rows: auto auto;
      
      .featured-item:nth-child(3) {
        grid-column: 1;
        grid-row: 2;
      }
    }
    
    // 当有4个时，完整的2x2网格
    &:has(.featured-item:nth-child(4):last-child) {
      grid-template-columns: 1fr 1fr;
      grid-template-rows: auto auto;
    }
  }
}

// 活动类型区域
.category-section {
  margin: 0;
  transition: all 0.3s ease;

  .category-container {
    // 按照设计图的渐变背景：从 #F3E5D2 到 #FFFFFF
    background: linear-gradient(180deg, #F3E5D2 0%, #FFFFFF 100%);
    border-radius: 16.5px 16.5px 0 0; // 只有上面有圆角，下面没有圆角
    padding: 20px 16px;
    transition: all 0.3s ease;

    .category-scroll {
      display: flex;
      gap: 0; // 无间距，均分

      // 如果类型数量不超过4个，均分显示
      &:not(.can-scroll) {
        .category-item {
          flex: 1; // 均分宽度
        }
      }

      // 如果类型数量超过4个，支持滑动
      &.can-scroll {
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;

        &::-webkit-scrollbar {
          display: none;
        }

        .category-item {
          flex: 0 0 auto;
          min-width: 80px; // 最小宽度
        }
      }

      .category-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 8px 4px;
        cursor: pointer;
        transition: all 0.3s ease;

        .category-icon-wrapper {
          margin-bottom: 8px;

          .category-icon {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            border: 1px dashed #D4A574;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;

            img {
              width: 32px;
              height: 32px;
              object-fit: contain;
            }

            // 全部活动的特殊图标
            &.all-icon {
              .all-icon-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                grid-template-rows: 1fr 1fr;
                gap: 4px;
                width: 20px;
                height: 20px;

                .grid-dot {
                  width: 6px;
                  height: 6px;
                  background: #D4A574;
                  border-radius: 50%;
                }
              }
            }
          }
        }

        .category-name {
          font-size: 12px;
          color: #333;
          text-align: center;
          line-height: 1.2;
          font-weight: 400;
        }

        // 激活状态
        &.active {
          .category-icon {
            border-color: #D4A574;
            background: rgba(212, 165, 116, 0.1);
            box-shadow: 0 2px 8px rgba(212, 165, 116, 0.2);
          }

          .category-name {
            color: #D4A574;
            font-weight: 600;
          }
        }
      }
    }
  }


}



// 筛选区域
.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: white;
  transition: all 0.3s ease;

  .sort-buttons {
    display: flex;
    gap: 132.5px; // 根据图片调整间距为132.5pt

    .sort-btn {
      background: #F5F5F5;
      border: none;
      color: #666;
      font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; // 苹方-简
      font-size: 11px; // 字号11pt
      font-weight: 400; // 字重Regular
      line-height: 15px; // 行高15pt
      letter-spacing: 0; // 字间距0pt
      padding: 6px 12px; // 减少内边距，让按钮更紧凑
      border-radius: 20px;
      display: flex;
      align-items: center;
      gap: 2px; // 减少图标和文字的间距
      cursor: pointer;
      transition: all 0.3s ease;
      text-align: left; // 左对齐

      &.active {
        background: linear-gradient(135deg, #9D8262 0%, #432A0C 100%); // 使用筛选弹窗的渐变样式
        border-color: transparent;
        color: white;
        font-weight: 400; // 保持Regular字重
        padding: 4px 8px; // 紧凑的内边距
        justify-content: flex-start; // 左对齐
      }

      &:hover {
        background: #E8E8E8;

        &.active {
          background: linear-gradient(135deg, #9D8262 0%, #432A0C 100%);
        }
      }
    }
  }

  .filter-btn {
    background: #F5F5F5;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;

    &.active {
      background: #D4A574;
      color: white;
    }

    &:hover {
      background: #E8E8E8;

      &.active {
        background: #D4A574;
      }
    }

    // 筛选图标
    &::after {
      content: '▼';
      margin-left: 4px;
      font-size: 10px;
      transition: transform 0.3s ease;
    }

    &.active::after {
      transform: rotate(180deg);
    }
  }


}

// 筛选区域样式（在sticky-section内部已定义）

.activity-list {
  padding: 0 16px 16px;

  .activity-item {
    background: white;
    border-radius: 8px;
    margin-bottom: 16px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .activity-title-wrapper {
      padding: 16px 16px 8px;
      border-bottom: 1px solid #f0f0f0;

      .activity-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 4px;
        line-height: 1.4;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .activity-code {
        font-size: 12px;
        color: #999;
      }
    }

    .activity-image {
      width: 100%;
      height: 160px;
      object-fit: cover;
    }

    .activity-info-section {
      padding: 16px;

      .info-row {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 14px;

        .info-label {
          color: #666;
          min-width: 80px;
        }

        .info-value {
          color: #333;
          flex: 1;
        }

        .activity-status {
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 12px;

          &.status-not-started {
            background: #f0f0f0;
            color: #666;
          }

          &.status-ongoing {
            background: #e8f5e8;
            color: #52c41a;
          }

          &.status-ended {
            background: #fff2e8;
            color: #fa8c16;
          }
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .activity-actions {
      padding: 0 16px 16px;

      .action-button {
        width: 100%;
        height: 44px;
        border-radius: 22px;
        border: none;
        font-size: 16px;
        font-weight: bold;
        cursor: pointer;

        &.primary {
          background: linear-gradient(135deg, #d4af37, #b8941f);
          color: white;
        }

        &.registered {
          background: #f0f0f0;
          color: #999;
          border: 1px solid #ddd;
        }
      }
    }
  }
}

.filter-popup {
  padding: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white; // 背景改为白色
  border-radius: 0;

  .filter-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: white; // 确保内容区域背景为白色

    .filter-group {
      margin-bottom: 24px;

      .filter-title {
        margin-bottom: 12px;
        font-size: 16px;
        font-weight: 600;
        color: #333;
        line-height: 1.2;
      }

      .filter-options {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;

        .filter-option-btn {
          width: 100px; // 宽度100pt
          height: 30.5px; // 高度30.5pt
          border: 0.56px solid #D6BCA4; // 边框粗细0.56pt，颜色#D6BCA4
          border-radius: 15.5px; // 圆角15.5pt
          background: white;
          color: #C58D5B; // 文字颜色#C58D5B
          font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; // 苹方-简
          font-size: 12px; // 字号12pt
          font-weight: 400; // 字重Regular
          line-height: 16.5px; // 行高16.5pt
          letter-spacing: 0; // 字间距0pt
          text-align: center; // 居中对齐
          cursor: pointer;
          transition: all 0.3s ease;
          outline: none;
          display: flex;
          align-items: center;
          justify-content: center;
          box-sizing: border-box;

          &:hover {
            border-color: #C58D5B;
            color: #C58D5B;
          }

          &.active {
            background: linear-gradient(135deg, #9D8262 0%, #432A0C 100%); // 选中渐变：从#9D8262到#432A0C
            border-color: transparent;
            color: white;
            font-weight: 400;
          }
        }
      }
    }
  }

  .filter-footer {
    display: flex;
    gap: 12px;
    padding: 20px;
    background: white;
    border-top: 1px solid #f0f0f0;

    .filter-reset-btn {
      flex: 1;
      height: 44px;
      border: 1px solid #E0E0E0;
      border-radius: 22px;
      background: white;
      color: #666;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      outline: none;

      &:hover {
        background: #F5F5F5;
        border-color: #D4A574;
        color: #D4A574;
      }
    }

    .filter-confirm-btn {
      flex: 1;
      height: 44px;
      border: none;
      border-radius: 22px;
      background: linear-gradient(135deg, #D4A574, #C19660);
      color: white;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      outline: none;
      box-shadow: 0 2px 8px rgba(212, 165, 116, 0.3);

      &:hover {
        background: linear-gradient(135deg, #C19660, #B8941F);
        box-shadow: 0 4px 12px rgba(212, 165, 116, 0.4);
      }

      &:active {
        transform: translateY(1px);
      }
    }
  }
}

</style>
